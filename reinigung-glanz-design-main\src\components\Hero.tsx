
import { useState, useEffect } from 'react';

interface HeroProps {
  scrollY: number;
}

const Hero = ({ scrollY }: HeroProps) => {
  return (
    <section
      id="home"
      className="min-h-screen flex items-center justify-center relative"
      style={{ padding: `${scrollY * 0.1}px var(--space-4) var(--section-padding-lg)` }}
    >
      <div
        className="text-center max-w-5xl mx-auto animate-fade-in"
        style={{
          transform: `translateY(${scrollY * 0.2}px)`,
        }}
      >
        <h1 className="suz-text-display-xl text-slate-800 mb-12">
          <span className="gradient-text-animated pulse-glow">Reinigung</span> neu definiert
        </h1>
        <p className="suz-text-heading-xl text-slate-600 mb-6 font-light">
          mit Stil, Präzision und Vertrauen
        </p>
        <p className="suz-text-body-xl text-slate-500 mb-16 max-w-3xl mx-auto" style={{ lineHeight: 'var(--line-height-relaxed)' }}>
          Zuverlässige Reinigung für Hotels, Büros und Wohnanlagen – von Profis für Profis.
        </p>

        <div className="flex flex-col sm:flex-row gap-6 justify-center">
          <a
            href="https://wa.me/4917623152477"
            target="_blank"
            rel="noopener noreferrer"
            className="suz-btn-primary bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 px-10 py-5 rounded-full text-lg shadow-2xl"
            aria-label="Kontakt über WhatsApp aufnehmen"
          >
            WhatsApp Kontakt
          </a>
          <a
            href="mailto:<EMAIL>"
            className="suz-btn-primary bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 px-10 py-5 rounded-full text-lg shadow-2xl"
            aria-label="E-Mail an SUZ Reinigung senden"
          >
            E-Mail senden
          </a>
        </div>
      </div>

      {/* Enhanced Floating Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="floating-element absolute top-20 left-10 w-40 h-40 bg-blue-300/20 rounded-full blur-3xl"></div>
        <div className="floating-element absolute top-40 right-20 w-32 h-32 bg-cyan-300/20 rounded-full blur-2xl" style={{animationDelay: '2s'}}></div>
        <div className="floating-element absolute bottom-20 left-1/4 w-48 h-48 bg-sky-300/20 rounded-full blur-3xl" style={{animationDelay: '4s'}}></div>
      </div>
    </section>
  );
};

export default Hero;
